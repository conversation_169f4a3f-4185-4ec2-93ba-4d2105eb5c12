const ServiceStatus = require('../models/ServiceStatus');

class ServiceStatusService {
  // Get service status by booking ID with authorization check
  static async getService(bookingId, userId) {
    try {
      if (!bookingId) {
        throw new Error('Booking ID is required');
      }

      // Verify user has access to this booking
      if (userId) {
        await this.verifyUserAccess(bookingId, userId);
      }

      const serviceStatus = await ServiceStatus.getByBookingId(bookingId);

      if (!serviceStatus) {
        throw new Error('Service status not found. Booking may not be accepted yet.');
      }

      return serviceStatus;
    } catch (error) {
      console.error('Error in getService:', error);
      throw new Error(`Service error: ${error.message}`);
    }
  }

  // Update service status - only nurses can update
  static async updateService(bookingId, status, userId) {
    try {
      if (!bookingId || !status) {
        throw new Error('Booking ID and status are required');
      }

      if (!userId) {
        throw new Error('User ID is required for authorization');
      }

      const validStatuses = ['not_started', 'started', 'completed', 'payment_received'];
      if (!validStatuses.includes(status)) {
        throw new Error('Invalid status provided');
      }

      // Verify user has permission to update this booking (must be the assigned nurse)
      await this.verifyNursePermission(bookingId, userId);

      // Get current status
      const currentStatus = await ServiceStatus.getByBookingId(bookingId);
      if (!currentStatus) {
        // If service status doesn't exist, create it first
        await ServiceStatus.create(bookingId, 'not_started');
        const newCurrentStatus = await ServiceStatus.getByBookingId(bookingId);
        if (!newCurrentStatus) {
          throw new Error('Failed to create service status record');
        }
      }

      // Get the latest current status after potential creation
      const latestStatus = await ServiceStatus.getByBookingId(bookingId);

      // Validate status transition
      const validationResult = await this.validateStatusTransition(latestStatus.status, status, bookingId);
      if (!validationResult.success) {
        return validationResult;
      }

      // Update service status
      await ServiceStatus.updateStatus(bookingId, status);
      const updatedStatus = await ServiceStatus.getByBookingId(bookingId);

      return {
        success: true,
        message: 'Service status updated successfully',
        data: updatedStatus
      };
    } catch (error) {
      console.error('Error in updateService:', error);
      return {
        success: false,
        message: `Service error: ${error.message}`,
        data: null
      };
    }
  }

  // Get all service statuses for a nurse
  static async getNurseService(nurseId) {
    try {
      if (!nurseId) {
        throw new Error('Nurse ID is required');
      }

      const serviceStatuses = await ServiceStatus.getAllByNurseId(nurseId);
      return serviceStatuses;
    } catch (error) {
      console.error('Error in getNurseService:', error);
      throw new Error(`Service error: ${error.message}`);
    }
  }

  // Get all service statuses for a customer
  static async getCustomerService(customerId) {
    try {
      if (!customerId) {
        throw new Error('Customer ID is required');
      }

      const serviceStatuses = await ServiceStatus.getAllByCustomerId(customerId);
      return serviceStatuses;
    } catch (error) {
      console.error('Error in getCustomerService:', error);
      throw new Error(`Service error: ${error.message}`);
    }
  }

  // Populate service status for accepted bookings (admin function)
  static async populateAcceptedBookings() {
    try {
      const affectedRows = await ServiceStatus.populateAcceptedBookings();
      return {
        success: true,
        message: `${affectedRows} service status records created`,
        data: { affectedRows }
      };
    } catch (error) {
      console.error('Error in populateAcceptedBookings:', error);
      throw new Error(`Service error: ${error.message}`);
    }
  }

  // Verify user has access to view booking (both nurse and customer)
  static async verifyUserAccess(bookingId, userId) {
    try {
      const { pool } = require("../config/database");

      if (!userId) {
        throw new Error('User ID is required for access verification');
      }

      const [rows] = await pool.execute(
        'SELECT booking_id FROM bookings WHERE booking_id = ? AND (nurse_cognitoId = ? OR customer_cognitoId = ?)',
        [bookingId, userId, userId]
      );

      if (rows.length === 0) {
        throw new Error('Unauthorized: You do not have permission to view this booking');
      }
    } catch (error) {
      console.error('Error in verifyUserAccess:', error);
      throw error;
    }
  }

  // Verify nurse has permission to update booking (only assigned nurse)
  static async verifyNursePermission(bookingId, nurseId) {
    try {
      const { pool } = require("../config/database");

      if (!nurseId) {
        throw new Error('Nurse ID is required for permission verification');
      }

      const [rows] = await pool.execute(
        'SELECT booking_id FROM bookings WHERE booking_id = ? AND nurse_cognitoId = ?',
        [bookingId, nurseId]
      );

      if (rows.length === 0) {
        throw new Error('Unauthorized: You do not have permission to update this booking');
      }
    } catch (error) {
      console.error('Error in verifyNursePermission:', error);
      throw error;
    }
  }

  // Validate status transition
  static async validateStatusTransition(currentStatus, newStatus, bookingId) {
    const validTransitions = {
      'not_started': ['started', 'cancelled', 'declined'],
      'started': ['completed'],
      'completed': ['payment_received'],
      'payment_received': [] // No further transitions allowed
    };

    if (!validTransitions[currentStatus] || !validTransitions[currentStatus].includes(newStatus)) {
      return {
        success: false,
        message: `Invalid status transition from ${currentStatus} to ${newStatus}`,
        data: null
      };
    }

    // Additional validation for 'started' status
    if (newStatus === 'started') {
      return await this.validateStartedStatusConstraints(bookingId);
    }

    return {
      success: true,
      message: 'Status transition is valid',
      data: null
    };
  }

  // Validate constraints for updating status to 'started'
  static async validateStartedStatusConstraints(bookingId) {
    try {
      const { pool } = require("../config/database");

      // First, verify the booking status is 'Accepted'
      const [bookingRows] = await pool.execute(
        'SELECT booking_status, nurse_cognitoId FROM bookings WHERE booking_id = ?',
        [bookingId]
      );

      if (bookingRows.length === 0) {
        return {
          success: false,
          message: 'Booking not found',
          data: null
        };
      }

      const booking = bookingRows[0];
      if (booking.booking_status !== 'Accepted') {
        return {
          success: false,
          message: 'Cannot start service: Booking status must be "Accepted"',
          data: null
        };
      }

      // Second, check that all previous bookings for the same nurse have 'completed' status
      const [previousBookings] = await pool.execute(`
        SELECT
          b.booking_id,
          b.booked_date,
          b.booked_slot,
          ss.status
        FROM bookings b
        LEFT JOIN service_status ss ON b.booking_id = ss.booking_id
        WHERE b.nurse_cognitoId = ?
          AND b.booking_id != ?
          AND b.booking_status = 'Accepted'
          AND (b.booked_date < (SELECT booked_date FROM bookings WHERE booking_id = ?)
               OR (b.booked_date = (SELECT booked_date FROM bookings WHERE booking_id = ?)
                   AND b.booked_slot < (SELECT booked_slot FROM bookings WHERE booking_id = ?)))
        ORDER BY b.booked_date DESC, b.booked_slot DESC
      `, [booking.nurse_cognitoId, bookingId, bookingId, bookingId, bookingId]);

      // Check if any previous booking doesn't have 'completed' status
      const incompleteBookings = previousBookings.filter(prevBooking =>
        !prevBooking.status || prevBooking.status !== 'completed'
      );

      if (incompleteBookings.length > 0) {
        return {
          success: false,
          message: `Cannot start service: Previous bookings must be completed before starting a new service`,
          data: null
        };
      }

      return {
        success: true,
        message: 'All constraints satisfied for starting service',
        data: null
      };

    } catch (error) {
      console.error('Error in validateStartedStatusConstraints:', error);
      return {
        success: false,
        message: `Validation error: ${error.message}`,
        data: null
      };
    }
  }
}

module.exports = ServiceStatusService;