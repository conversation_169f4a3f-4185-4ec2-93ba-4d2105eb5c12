const db = require("../config/database");
const { generateSignedUrl } = require("../utils/s3Utils");

const createBooking = async (req, res) => {
  try {
    const {
      nurse_cognitoId,
      customer_given_name,
      customer_booked_location_address,
      customer_booked_location_latitude,
      customer_booked_location_longitude,
      services_selected,
      hourly_fare,
      booked_date,
      booked_slot,
    } = req.body;

    // Get customer_cognitoId from authenticated user
    const customer_cognitoId = req.user.sub;

    // Validate that the authenticated user is a customer
    if (req.user.userType !== 'customer') {
      return res.status(403).json({
        error: "Only customers can create bookings",
        userType: req.user.userType
      });
    }

    // Fetch nurse details from test_nurserv_api.users
    const [nurseRows] = await db.pool.query(
      `SELECT cognito_id, given_name, latitude, longitude, address
       FROM test_nurserv_api.users
       WHERE cognito_id = ?`,
      [nurse_cognitoId]
    );

    if (!nurseRows.length) {
      return res.status(404).json({ error: "Nurse not found" });
    }

    const nurse = nurseRows[0];

    // Check nurse availability and declined bookings in one query
    const [conflictCheck] = await db.pool.query(
      `SELECT 
        SUM(CASE WHEN booking_status NOT IN ('Cancelled', 'Declined') THEN 1 ELSE 0 END) as existing_bookings,
        SUM(CASE WHEN booking_status = 'Declined' AND customer_cognitoId = ? THEN 1 ELSE 0 END) as declined_bookings
       FROM bookings 
       WHERE nurse_cognitoId = ? AND booked_date = ? AND booked_slot = ?`,
      [customer_cognitoId, nurse_cognitoId, booked_date, booked_slot]
    );

    const { existing_bookings, declined_bookings } = conflictCheck[0];

    if (existing_bookings > 0) {
      return res.status(409).json({
        error: "Nurse is not available for the selected date and time slot"
      });
    }

    if (declined_bookings > 0) {
      return res.status(409).json({
        error: "You cannot rebook this nurse for the same date and time slot after being declined"
      });
    }

    const bookingId = Math.floor(Date.now() / 1000);
    const servicesSelectedString = Array.isArray(services_selected)
      ? JSON.stringify(services_selected)
      : JSON.stringify([services_selected]);

    const [result] = await db.pool.query(
      `INSERT INTO bookings (
        booking_id,
        nurse_cognitoId,
        customer_cognitoId,
        nurse_given_name,
        nurse_location_latitude,
        nurse_location_longitude,
        nurse_location_address,
        customer_given_name,
        customer_booked_location_address,
        customer_booked_location_latitude,
        customer_booked_location_longitude,
        services_selected,
        hourly_fare,
        booked_date,
        booked_slot,
        created_at,
        updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CONVERT_TZ(NOW(), 'UTC', '+05:30'), CONVERT_TZ(NOW(), 'UTC', '+05:30'))`,
      [
        bookingId,
        nurse.cognito_id,
        customer_cognitoId,
        nurse.given_name,
        nurse.latitude,
        nurse.longitude,
        nurse.address,
        customer_given_name,
        customer_booked_location_address,
        customer_booked_location_latitude,
        customer_booked_location_longitude,
        servicesSelectedString,
        hourly_fare,
        booked_date,
        booked_slot
      ]
    );

    return res.status(201).json({
      message: "Booking created successfully",
      booking_id: bookingId,
      booking_status: "Pending",
      booking_date: booked_date,
      id: result.insertId,
      nurse: nurse
    });
  } catch (error) {
    console.error("Error creating booking:", error);
    return res.status(500).json({
      error: "Internal server error",
    });
  }
};

// Update booking status
const updateBookingStatus = async (req, res) => {
  try {
    const { booking_id } = req.params;
    const { booking_status, cancellation_reason_id, custom_reason } = req.body;
    const userSub = req.user.sub;
    const userType = req.user.userType;

    // Validate booking status
    const validStatuses = ["Pending", "Accepted", "Declined", "Cancelled", "Completed"];
    if (!validStatuses.includes(booking_status)) {
      return res.status(400).json({
        error: "Invalid booking status. Must be one of: Pending, Accepted, Declined, Cancelled, Completed",
      });
    }

    // Get the current booking to check ownership and current status
    const [bookings] = await db.pool.query(
      "SELECT * FROM bookings WHERE booking_id = ?",
      [booking_id]
    );

    if (!bookings || bookings.length === 0) {
      return res.status(404).json({
        error: "Booking not found",
      });
    }

    const booking = bookings[0];

    // Authorization checks based on user type and action
    if (userType === 'customer') {
      // Customers can only cancel their own bookings
      if (booking.customer_cognitoId !== userSub) {
        return res.status(403).json({
          error: "You can only modify your own bookings"
        });
      }

      if (booking_status !== 'Cancelled') {
        return res.status(403).json({
          error: "Customers can only cancel bookings"
        });
      }

      // Can't cancel already completed or cancelled bookings
      if (['Completed', 'Cancelled'].includes(booking.booking_status)) {
        return res.status(400).json({
          error: `Cannot cancel a ${booking.booking_status.toLowerCase()} booking`
        });
      }
    } else if (userType === 'nurse') {
      // Nurses can accept/decline their own bookings
      if (booking.nurse_cognitoId !== userSub) {
        return res.status(403).json({
          error: "You can only modify bookings assigned to you"
        });
      }

      // Nurses can accept, decline, or complete bookings
      if (!['Accepted', 'Declined', 'Completed'].includes(booking_status)) {
        return res.status(403).json({
          error: "Nurses can only accept, decline, or complete bookings"
        });
      }

      // Can't modify cancelled bookings
      if (booking.booking_status === 'Cancelled') {
        return res.status(400).json({
          error: "Cannot modify a cancelled booking"
        });
      }

      // Validation for decline with custom reason
      if (booking_status === 'Declined') {
        if (!custom_reason || custom_reason.trim() === '') {
          return res.status(400).json({
            error: "Custom reason is required when declining a booking"
          });
        }

        // Validate custom reason length (optional - adjust as needed)
        if (custom_reason.trim().length > 500) {
          return res.status(400).json({
            error: "Custom reason must be less than 500 characters"
          });
        }
      }
    } else {
      return res.status(403).json({
        error: "Invalid user type"
      });
    }

    // Handle different update scenarios
    let updateQuery;
    let updateParams;

    // Case 1: Customer cancelling with cancellation reason ID
    if (cancellation_reason_id && booking_status === 'Cancelled' && userType === 'customer') {
      // Get the reason text from the cancellation_reasons table
      const [reasons] = await db.pool.query(
        "SELECT cancellation_reasons FROM cancellation_reasons WHERE id = ?",
        [cancellation_reason_id]
      );

      if (!reasons || reasons.length === 0) {
        return res.status(400).json({
          error: "Invalid cancellation reason ID",
        });
      }

      updateQuery = "UPDATE bookings SET booking_status = ?, cancellation_reason = ?, updated_at = CONVERT_TZ(NOW(), 'UTC', '+05:30') WHERE booking_id = ?";
      updateParams = [booking_status, reasons[0].cancellation_reasons, booking_id];

      // Also update service_status table if booking_id exists there
      try {
        // Check if booking_id exists in service_status table
        const [serviceStatusRows] = await db.pool.query(
          "SELECT booking_id FROM service_status WHERE booking_id = ?",
          [booking_id]
        );

        // If booking_id exists in service_status table, update status to 'Cancelled'
        if (serviceStatusRows && serviceStatusRows.length > 0) {
          await db.pool.query(
            "UPDATE service_status SET status = ?, updated_at = CONVERT_TZ(NOW(), 'UTC', '+05:30') WHERE booking_id = ?",
            ['Cancelled', booking_id]
          );
        }
        // If booking_id doesn't exist in service_status table, skip the update (no error)
      } catch (serviceStatusError) {
        console.error("Error updating service_status table:", serviceStatusError);
        // Continue with booking update even if service_status update fails
      }
    }

    // Case 2: Nurse declining with custom reason
    else if (custom_reason && booking_status === 'Declined' && userType === 'nurse') {
      updateQuery = "UPDATE bookings SET booking_status = ?, cancellation_reason = ?, updated_at = CONVERT_TZ(NOW(), 'UTC', '+05:30') WHERE booking_id = ?";
      updateParams = [booking_status, custom_reason.trim(), booking_id];
    }
    // Case 3: Regular status update without reason
    else {
      updateQuery = "UPDATE bookings SET booking_status = ?, updated_at = CONVERT_TZ(NOW(), 'UTC', '+05:30') WHERE booking_id = ?";
      updateParams = [booking_status, booking_id];
    }

    // Execute the update query
    const [result] = await db.pool.query(updateQuery, updateParams);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        error: "Booking not found or no changes made",
      });
    }

    // Prepare response message based on action
    let message = "Booking status updated successfully";
    if (booking_status === 'Declined' && custom_reason) {
      message = "Booking declined successfully with custom reason";
    } else if (booking_status === 'Cancelled' && cancellation_reason_id) {
      message = "Booking cancelled successfully";
    }

    return res.json({
      message: message,
      booking_id: booking_id,
      booking_status: booking_status,
    });
  } catch (error) {
    console.error("Error updating booking:", error);
    return res.status(500).json({
      error: "Internal server error",
    });
  }
};

// Get all bookings (should be restricted to admin users)
const getAllBookings = async (req, res) => {
  try {

    const [bookings] = await db.pool.query(
      "SELECT * FROM bookings ORDER BY created_at DESC"
    );

    return res.json({
      total_bookings: bookings.length,
      bookings: bookings,
    });
  } catch (error) {
    console.error("Error fetching bookings:", error);
    return res.status(500).json({
      error: "Internal server error",
    });
  }
};

// Get booking by ID
const getBookingById = async (req, res) => {
  try {
    const { booking_id } = req.params;

    const [bookings] = await db.pool.query(
      "SELECT * FROM bookings WHERE booking_id = ?",
      [booking_id]
    );

    if (!bookings || bookings.length === 0) {
      return res.status(404).json({
        error: "Booking not found",
      });
    }

    const booking = bookings[0];

    return res.json({
      booking: booking,
    });
  } catch (error) {
    console.error("Error fetching booking:", error);
    return res.status(500).json({
      error: "Internal server error",
    });
  }
};

// Get bookings by customer
const getBookingsByCustomer = async (req, res) => {
  try {
    const { customer_cognitoId } = req.params;
    // Join bookings with nurse profile data to get profile image information
    const [bookings] = await db.pool.query(
      `SELECT
        b.*,
        u.profile_image_name,
        u.s3_key as nurse_profile_s3_key
       FROM bookings b
       LEFT JOIN test_nurserv_api.users u ON b.nurse_cognitoId = u.cognito_id
       WHERE b.customer_cognitoId = ?
       ORDER BY b.created_at DESC`,
      [customer_cognitoId]
    );
    // Generate signed URLs for nurse profile images
    const bookingsWithSignedUrls = await Promise.all(
      bookings.map(async (booking) => {
        let nurseProfileImageSignedUrl = null;
        if (booking.nurse_profile_s3_key) {
          try {
            nurseProfileImageSignedUrl = await generateSignedUrl(booking.nurse_profile_s3_key);
          } catch (error) {
            console.warn(`Failed to generate signed URL for nurse profile image in booking ${booking.booking_id}:`, error);
            nurseProfileImageSignedUrl = null;
          }
        }
        return {
          ...booking,
          nurse_profile_image_signed_url: nurseProfileImageSignedUrl
        };
      })
    );
    return res.json({
      total_bookings: bookingsWithSignedUrls.length,
      bookings: bookingsWithSignedUrls,
    });
  } catch (error) {
    console.error("Error fetching customer bookings:", error);
    return res.status(500).json({
      error: "Internal server error",
    });
  }
};

// Get bookings by nurse
const getBookingsByNurse = async (req, res) => {
  try {
    const { nurse_cognitoId } = req.params;

    const [bookings] = await db.pool.query(
      "SELECT * FROM bookings WHERE nurse_cognitoId = ? ORDER BY created_at DESC",
      [nurse_cognitoId]
    );

    return res.json({
      total_bookings: bookings.length,
      bookings: bookings,
    });
  } catch (error) {
    console.error("Error fetching nurse bookings:", error);
    return res.status(500).json({
      error: "Internal server error",
    });
  }
};

module.exports = {
  createBooking,
  updateBookingStatus,
  getAllBookings,
  getBookingById,
  getBookingsByCustomer,
  getBookingsByNurse,
};

